"""
MySQL数据库配置模块

提供MySQL数据库连接池配置
"""

from config import settings


def get_mysql_pool_config():
    """获取MySQL连接池配置"""
    return {
        'pool_name': settings.MYSQL_POOL_NAME,
        'pool_size': settings.MYSQL_POOL_SIZE,
        'pool_reset_session': settings.MYSQL_POOL_RESET_SESSION,
        'host': settings.MYSQL_HOST,
        'port': settings.MYSQL_PORT,
        'user': settings.MYSQL_USER,
        'password': settings.MYSQL_PASSWORD,
        'database': settings.MYSQL_DATABASE,
        'charset': settings.MYSQL_CHARSET,
        'autocommit': settings.MYSQL_AUTOCOMMIT,
        'connect_timeout': settings.MYSQL_CONNECT_TIMEOUT,
        'sql_mode': settings.MYSQL_SQL_MODE,
        'time_zone': settings.MYSQL_TIME_ZONE,
        'buffered': settings.MYSQL_BUFFERED
    }
