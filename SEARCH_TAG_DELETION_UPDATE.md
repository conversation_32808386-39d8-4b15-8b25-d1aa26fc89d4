# 搜索标签命令消息删除功能更新

## 更新概述

根据用户需求，现在**用户使用搜索标签命令的消息也会在1分钟后自动删除**。

## 更新内容

### 修改文件
- `src/handlers/teacher_handler.py` - 搜索标签处理器

### 功能变更

在 `handle_search_tag_command` 函数中，为所有情况都添加了删除用户命令消息的逻辑：

1. **成功搜索情况**
   - 删除搜索结果消息（1分钟后）
   - **新增：删除用户的搜索命令消息（1分钟后）**

2. **错误情况**
   - 删除错误提示消息（1分钟后）
   - **新增：删除用户的搜索命令消息（1分钟后）**

3. **参数缺失情况**
   - 删除使用说明消息（1分钟后）
   - **新增：删除用户的搜索命令消息（1分钟后）**

4. **非群组使用情况**
   - 删除群组限制提示消息（1分钟后）
   - **新增：删除用户的搜索命令消息（1分钟后）**

### 代码示例

```python
# 1分钟后自动删除用户的搜索命令消息
context.job_queue.run_once(
    callback=lambda context: delete_message_by_id(
        context, message.chat.id, message.message_id
    ),
    when=60  # 60秒 = 1分钟
)
```

## 测试验证

✅ 已通过单元测试验证所有情况下都正确设置了消息删除任务：
- 成功搜索删除消息测试通过
- 错误情况删除消息测试通过  
- 无参数删除消息测试通过
- 私聊限制删除消息测试通过

## 用户体验改进

现在当用户在群组中使用 `/search_tag` 命令时：

1. 用户发送：`/search_tag 数学,高中`
2. 机器人回复搜索结果
3. **1分钟后，用户的命令消息和机器人的回复消息都会被自动删除**

这样可以保持群组的整洁，避免搜索命令和结果消息长期占用聊天记录。

## 日志更新

日志消息也已更新，现在会显示：
```
命令标签搜索完成，返回 X 个结果，搜索命令和结果消息将在1分钟后自动删除
```

## 兼容性

- ✅ 向后兼容，不影响现有功能
- ✅ 保持原有的1分钟删除时间设置
- ✅ 使用相同的 `delete_message_by_id` 辅助函数
- ✅ 错误处理机制保持不变
